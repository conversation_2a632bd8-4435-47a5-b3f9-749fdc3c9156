# Requirements for runtime.
pydantic-settings

python-Levenshtein
scikit-learn
filelock
loguru
fire
fuzzywuzzy
openai
litellm
azure.identity
pyarrow
rich
tqdm
typer

numpy # we use numpy as default data format. So we have to install numpy
pandas # we use pandas as default data format. So we have to install pandas
pandarallel # parallelize pandas
matplotlib
langchain
langchain-community
tiktoken
pymupdf  # Extract shotsreens from pdf

# PDF related
pypdf
azure-ai-formrecognizer

# factor implementations
tables

# CI Fix Tool
tree-sitter-python
tree-sitter

python-dotenv

# infrastructure related.
docker

# demo related
streamlit
plotly
st-theme
randomname
flask
flask-cors

# kaggle crawler
selenium
kaggle
nbformat

# tool
setuptools-scm
seaborn
azure.ai.inference

# data folder desc
humanize
genson

# mlflow
mlflow
azureml-mlflow
types-pytz

# data science scenario for custom dataset
sparse