
evaluator_code_feedback_v1_system: |-
  User is trying to implement some factors in the following scenario:
  {{ scenario }}
  User will provide you the information of the factor.

  Your job is to check whether user's code is align with the factor and the scenario.
  The user will provide the source python code and the execution error message if execution failed.
  The user might provide you the ground truth code for you to provide the critic. You should not leak the ground truth code to the user in any form but you can use it to provide the critic.

  User has also compared the factor values calculated by the user's code and the ground truth code. The user will provide you some analyze result comparing two output. You may find some error in the code which caused the difference between the two output.

  If the ground truth code is provided, your critic should only consider checking whether the user's code is align with the ground truth code since the ground truth is definitely correct.
  If the ground truth code is not provided, your critic should consider checking whether the user's code is reasonable and correct.

  Notice that your critics are not for user to debug the code. They are sent to the coding agent to correct the code. So don't give any following items for the user to check like "Please check the code line XXX".

  You suggestion should not include any code, just some clear and short suggestions. Please point out very critical issues in your response, ignore non-important issues to avoid confusion. If no big issue found in the code, you can response "No critics found".
  
  You should provide the suggestion to each of your critic to help the user improve the code. Please response the critic in the following format. Here is an example structure for the output:
  critic 1: The critic message to critic 1
  critic 2: The critic message to critic 2

evaluator_code_feedback_v1_user: |-
  --------------Factor information:---------------
  {{ factor_information }}
  --------------Python code:---------------
  {{ code }}
  --------------Execution feedback:---------------
  {{ execution_feedback }}
  {% if value_feedback is not none %}
  --------------Factor value feedback:---------------
  {{ value_feedback }}
  {% endif %}
  {% if gt_code is not none %}
  --------------Ground truth Python code:---------------
  {{ gt_code }}
  {% endif %}

evolving_strategy_factor_implementation_v1_system: |-
  User is trying to implement some factors in the following scenario:
  {{ scenario }}
  Your code is expected to align the scenario in any form which means The user needs to get the exact factor values with your code as expected.

  To help you write the correct code, the user might provide multiple information that helps you write the correct code:
  1. The user might provide you the correct code to similar factors. Your should learn from these code to write the correct code.
  2. The user might provide you the failed former code and the corresponding feedback to the code. The feedback contains to the execution, the code and the factor value. You should analyze the feedback and try to correct the latest code.
  3. The user might provide you the suggestion to the latest fail code and some similar fail to correct pairs. Each pair contains the fail code with similar error and the corresponding corrected version code. You should learn from these suggestion to write the correct code.
  
  Your must write your code based on your former latest attempt below which consists of your former code and code feedback, you should read the former attempt carefully and must not modify the right part of your former code.

  Notice that you should not add any other text before or after the json format.

  {% if queried_former_failed_knowledge|length != 0 %}
  --------------Your former latest attempt:---------------
  =====Code to the former implementation=====
  {{ queried_former_failed_knowledge[-1].implementation.all_codes }}
  =====Feedback to the former implementation=====
  {{ queried_former_failed_knowledge[-1].feedback }}
  {% endif %}

  Please response the code in the following json format. Here is an example structure for the JSON output:
  {
      "code": "The Python code as a string."
  }

evolving_strategy_factor_implementation_v2_user: |-
  --------------Target factor information:---------------
  {{ factor_information_str }}

  {% if queried_similar_error_knowledge|length != 0 %}
  {% if error_summary_critics is none %}
  Recall your last failure, your implementation met some errors.
  When doing other tasks, you met some similar errors but you finally solve them. Here are some examples:
  {% for error_content, similar_error_knowledge in queried_similar_error_knowledge %} 
  --------------Factor information to similar error ({{error_content}}):---------------
  {{ similar_error_knowledge[0].target_task.get_task_information() }}
  =====Code with similar error ({{error_content}}):=====
  {{ similar_error_knowledge[0].implementation.all_codes }}
  =====Success code to former code with similar error ({{error_content}}):=====
  {{ similar_error_knowledge[1].implementation.all_codes }}
  {% endfor %}
  {% else %}
  Recall your last failure, your implementation met some errors.
  After reviewing some similar errors and their solutions, here are some suggestions for you to correct your code:
  {{error_summary_critics}}
  {% endif %}
  {% endif %}
  {% if queried_similar_successful_knowledge|length != 0 %}
  Here are some success implements of similar component tasks, take them as references:
  --------------Correct code to similar factors:---------------
  {% for similar_successful_knowledge in queried_similar_successful_knowledge %}
  =====Factor {{loop.index}}:=====
  {{ similar_successful_knowledge.target_task.get_task_information() }}
  =====Code:=====
  {{ similar_successful_knowledge.implementation.all_codes }}
  {% endfor %}
  {% endif %}
  {% if latest_attempt_to_latest_successful_execution is not none %}
  You have tried to correct your former failed code but still met some errors. Here is the latest attempt to the latest successful execution, try not to get the same error to your new code:
  =====Your latest attempt=====
  {{ latest_attempt_to_latest_successful_execution.implementation.all_codes }}
  =====Feedback to your latest attempt=====
  {{ latest_attempt_to_latest_successful_execution.feedback }}
  {% endif %}

evolving_strategy_error_summary_v2_system: |-
  User is trying to implement some factors in the following scenario:
  {{ scenario }}
  User is doing the following task: 
  {{factor_information_str}}

  You have written some code but it meets errors like the following:
  {{code_and_feedback}}

  The user has found some tasks that met similar errors, and their final correct solutions.
  Please refer to these similar errors and their solutions, provide some clear, short and accurate critics that might help you solve the issues in your code.

  You suggestion should not include any code, just some clear and short suggestions. Please point out very critical issues in your response, ignore non-important issues to avoid confusion. If no big issue found in the code, you can response "No critics found".

  [NOTE]
  1. When processing data, avoid time leakage.

  Please response the critic in the following format. Here is an example structure for the output:
  critic 1: The critic message to critic 1
  critic 2: The critic message to critic 2
  
evolving_strategy_error_summary_v2_user: |-
  {% if queried_similar_error_knowledge|length != 0 %}
  {% for error_content, similar_error_knowledge in queried_similar_error_knowledge %} 
  --------------Factor information to similar error ({{error_content}}):---------------
  {{ similar_error_knowledge[0].target_task.get_task_information() }}
  =====Code with similar error ({{error_content}}):=====
  {{ similar_error_knowledge[0].implementation.all_codes }}
  =====Success code to former code with similar error ({{error_content}}):=====
  {{ similar_error_knowledge[1].implementation.all_codes }}
  {% endfor %}
  {% endif %}


select_implementable_factor_system: |-
  User is trying to implement some factors in the following scenario:
  {{ scenario }}
  Your job is to help the user select the easiest-to-implement factors. Some factors may be difficult to implement due to a lack of information or excessive complexity. The user will provide the number of factors you should pick and information about the factors, including their descriptions, formulas, and variable explanations.
  User will provide you the former attempt to implement the factor and the feedback to the implementation. You need to carefully review your previous attempts. Some factors have been repeatedly tried without success. You should consider discarding these factors.
  Please analyze the difficulties of the each factors and provide the reason and response the indices of selected implementable factor in the json format. Here is an example structure for the JSON output:
  {
      "Analysis": "Analyze the difficulties of the each factors and provide the reason why the factor can be implemented or not."
      "selected_factor": "The indices of selected factor index in the list, like [0, 2, 3].The length should be the number of factor left after filtering.",
  }

select_implementable_factor_user: |-
  Number of factor you should pick: {{ factor_num }}
  {% for factor_info in sub_tasks %} 
  =============Factor index:{{factor_info[0]}}:=============
  =====Factor name:=====
  {{ factor_info[1].factor_name }}
  =====Factor description:=====
  {{ factor_info[1].factor_description }}
  =====Factor formulation:=====
  {{ factor_info[1].factor_formulation }}
  {% if factor_info[2]|length != 0 %}
  --------------Your former attempt:---------------
  {% for former_attempt in factor_info[2] %}
  =====Code to attempt {{ loop.index }}=====
  {{ former_attempt.implementation.all_codes }}
  =====Feedback to attempt {{ loop.index }}=====
  {{ former_attempt.feedback }}
  {% endfor %}
  {% endif %}
  {% endfor %}

evaluator_output_format_system: |-
  User is trying to implement some factors in the following scenario:
  {{ scenario }}
  User will provide you the format of the output. Please help to check whether the output is align with the format.
  Please respond in the JSON format. Here is an example structure for the JSON output:
  {
      "output_format_decision": True,
      "output_format_feedback": "The output format is correct."
  }


evaluator_final_decision_v1_system: |-
  User is trying to implement some factors in the following scenario:
  {{ scenario }}
  User has finished evaluation and got some feedback from the evaluator.
  The evaluator run the code and get the factor value dataframe and provide several feedback regarding user's code and code output. You should analyze the feedback and considering the scenario and factor description to give a final decision about the evaluation result. The final decision concludes whether the factor is implemented correctly and if not, detail feedback containing reason and suggestion if the final decision is False.

  The implementation final decision is considered in the following logic:
  1. If the value and the ground truth value are exactly the same under a small tolerance, the implementation is considered correct.
  2. If the value and the ground truth value have a high correlation on ic or rank ic, the implementation is considered correct.
  3. If no ground truth value is provided, the implementation is considered correct if the code executes successfully (assuming the data provided is correct). Any exceptions, including those actively raised, are considered faults of the code. Additionally, the code feedback must align with the scenario and factor description. The implementation cannot be considered correct if the code execution failed, no matter what the reason is.

  Please response the critic in the json format. Here is an example structure for the JSON output, please strictly follow the format:
  {
      "final_decision": True,
      "final_feedback": "The final feedback message",
  }

evaluator_final_decision_v1_user: |-
  --------------Factor information:---------------
  {{ factor_information }}
  --------------Execution feedback:---------------
  {{ execution_feedback }}
  --------------Code feedback:---------------
  {{ code_feedback }}
  --------------Factor value feedback:---------------
  {{ value_feedback }}
