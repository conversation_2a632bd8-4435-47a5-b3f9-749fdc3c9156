extract_model_formulation_system: |-
    offer description of the proposed model in this paper, write a latex formula with variable as well as the architecture of the model. the format should be like 
    {
    "model_name (The name of the model)": {
        "description": "A detailed description of the model",
        "formulation": "A LaTeX formula representing the model's formulation",
        "architecture": "A detailed description of the model's architecture, e.g., neural network layers or tree structures",
        "variables": {
            "\\hat{y}_u": "The predicted output for node u",
            "variable_name_2": "Description of variable 2",
            "variable_name_3": "Description of variable 3"
        },
        "hyperparameters": {
            "hyperparameter_name_1": "value of hyperparameter 1",
            "hyperparameter_name_2": "value of hyperparameter 2",
            "hyperparameter_name_3": "value of hyperparameter 3"
        },
        "model_type": "Tabular or TimeSeries or Graph or XGBoost"  # Should be one of "Tabular", "TimeSeries", "Graph", or "XGBoost"
    }
    }
    Eg. 
    {
    "ABC Model": {
        "description": "A detailed description of the model",
        "formulation": "A LaTeX formula representing the model's formulation",
        "architecture": "A detailed description of the model's architecture, e.g., neural network layers or tree structures",
        "variables": {
            "\\hat{y}_u": "The predicted output for node u",
            "variable_name_2": "Description of variable 2",
            "variable_name_3": "Description of variable 3"
        },
        "hyperparameters": {
            "hyperparameter_name_1": "value of hyperparameter 1",
            "hyperparameter_name_2": "value of hyperparameter 2",
            "hyperparameter_name_3": "value of hyperparameter 3"
        },
        "model_type": "Tabular or TimeSeries or Graph or RandomForest or XGBoost"  # If torch & Neural network models are required, the choice should be one of "Tabular", "TimeSeries", or "Graph" 
    }
    }
    such format content should be begin with ```json and end with ``` and the content should be in json format.

evolving_strategy_model_coder:
    system: |-
        User is trying to implement some pytorch models in the following scenario:
        {{ scenario }}
        Your code is expected to align the scenario in any form which means The user needs to get the prediction of the model based on the input data.

        To help you write the correct code, the user might provide multiple information that helps you write the correct code:
        1. The user might provide you the correct code to similar models. Your should learn from these code to write the correct code.
        2. The user might provide you the failed former code and the corresponding feedback to the code. The feedback contains to the execution, the code and the model output value. You should analyze the feedback and try to correct the latest code.
        3. The user might provide you the suggestion to the latest fail code and some similar fail to correct pairs. Each pair contains the fail code with similar error and the corresponding corrected version code. You should learn from these suggestion to write the correct code.

        Your must write your code based on your former latest attempt below which consists of your former code and code feedback, you should read the former attempt carefully and must not modify the right part of your former code.

        {% if current_code is not none %}
        User has write some code before. You should write the new code based on this code. Here is the latest code:
        ```python
        {{ current_code }}
        ```
        Your code should be very similar to the former code which means your code should be ninety more percent same as the former code! You should not modify the right part of the code.
        {% else %}
        User has not write any code before. You should write the new code from scratch.
        {% endif %}

        {% if queried_former_failed_knowledge|length != 0 %}
        --------------Your former latest attempt:---------------
        =====Code to the former implementation=====
        {{ queried_former_failed_knowledge[-1].implementation.all_codes }}
        =====Feedback to the former implementation=====
        {{ queried_former_failed_knowledge[-1].feedback }}
        {% endif %}
        
        Please response the code in the following json format. Here is an example structure for the JSON output:
        {
            "code": "The Python code as a string."
        }

    user: |-
        --------------Target model information:---------------
        {{ model_information_str }}

        {% if queried_similar_successful_knowledge|length != 0 %}
        --------------Correct code to similar models:---------------
        {% for similar_successful_knowledge in queried_similar_successful_knowledge %}
        =====Model {{loop.index}}:=====
        {{ similar_successful_knowledge.target_task.get_task_information() }}
        =====Code:=====
        {{ similar_successful_knowledge.implementation.all_codes }}
        {% endfor %}
        {% endif %}

        {% if queried_former_failed_knowledge|length != 0 %}
        --------------Former failed code:---------------
        {% for former_failed_knowledge in queried_former_failed_knowledge %}
        =====Code to implementation {{ loop.index }}=====
        {{ former_failed_knowledge.implementation.all_codes }}
        =====Feedback to implementation {{ loop.index }}=====
        {{ former_failed_knowledge.feedback }}
        {% endfor %}
        {% endif %}

evaluator_code_feedback:
    system: |-
        User is trying to implement some models in the following scenario:
        {{ scenario }}
        User will provide you the information of the model.

        Your job is to check whether user's code is align with the model information and the scenario.
        The user will provide the source python code and the execution error message if execution failed.
        The user might provide you the ground truth code for you to provide the critic. You should not leak the ground truth code to the user in any form but you can use it to provide the critic.

        User has also compared the output generated by the user's code and the ground truth code. The user will provide you some analysis results comparing two output. You may find some error in the code which caused the difference between the two output.

        If the ground truth code is provided, your critic should only consider checking whether the user's code is align with the ground truth code since the ground truth is definitely correct.
        If the ground truth code is not provided, your critic should consider checking whether the user's code is reasonable and correct to the description and to the scenario.

        Notice that your critics are not for user to debug the code. They are sent to the coding agent to correct the code. So don't give any following items for the user to check like "Please check the code line XXX".

        You suggestion should not include any code, just some clear and short suggestions. Please point out very critical issues in your response, ignore non-important issues to avoid confusion. If no big issue found in the code, you can response "No critics found".

        You should provide the suggestion to each of your critic to help the user improve the code. Please response the critic in the following format. Here is an example structure for the output:
        critic 1: The critic message to critic 1
        critic 2: The critic message to critic 2
    
    user: |-
        --------------Model information:---------------
        {{ model_information }}
        --------------Python code:---------------
        {{ code }}
        --------------Execution feedback:---------------
        {{ model_execution_feedback }}
        {% if model_value_feedback is not none %}
        --------------Model value feedback:---------------
        {{ model_value_feedback }}
        {% endif %}
        {% if gt_code is not none %}
        --------------Ground truth Python code:---------------
        {{ gt_code }}
        {% endif %}


evaluator_final_feedback:
    system: |-
        User is trying to implement a model in the following scenario:
        {{ scenario }}
        User has finished evaluation and got some feedback from the evaluator.
        The evaluator run the code and get the output and provide several feedback regarding user's code and code output. You should analyze the feedback and considering the scenario and model description to give a final decision about the evaluation result. The final decision concludes whether the model is implemented correctly and if not, detail feedback containing reason and suggestion if the final decision is False.

        The implementation final decision is considered in the following logic:
        1. If the value and the ground truth value are exactly the same under a small tolerance, the implementation is considered correct.
        2. If no ground truth value is not provided, the implementation is considered correct if the code execution is successful and the code feedback is align with the scenario and model description.

        Please response the critic in the json format. Here is an example structure for the JSON output, please strictly follow the format:
        {
            "final_decision": True,
            "final_feedback": "The final feedback message",
        }
    user: |-
        --------------Model information:---------------
        {{ model_information }}
        --------------Model Execution feedback:---------------
        {{ model_execution_feedback }}
        --------------Model shape feedback:---------------
        {{ model_shape_feedback }}
        --------------Model Code feedback:---------------
        {{ model_code_feedback }}
        --------------Model value feedback:---------------
        {{ model_value_feedback }}