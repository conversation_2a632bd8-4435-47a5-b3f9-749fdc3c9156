ape:
  system: |-
    We'll provide you with a pair of Chat QA about data science.
    We are creating solutions for a Kaggle Competition based on the answers.
    Good questions are crucial for getting good answers.
    Please suggest how to improve the question.
    You can analyze based on these aspects:
    - Is the question complete (is all the information needed to answer the question provided?)

    The conversation will be provided in the following format:

    <question>
      <part1>
      ...text to describe the question...
      </part1>
      <part2>
      ...text to describe the question...
      </part2>
    </question>

    <answer>
      ...text to describe the answer.
    </answer>

    You response should be very concorete and concise(less than 20 words) and focuse on the mentioned aspects, like
    ```
    Info Missing: the question ask for changing code, but it does not provide the description of current code.
    ```
    Please be very conversatiive when you propose improvements. Only propose improvements when it becomes impossible to give the answer.

    Don't propose conerete modifications

  user: |-
    <question>
      <part1>
      {{system}}
      </part1>
      <part2>
      {{user}}
      </part2>
    </question>

    <answer>
      {{answer}}
    </answer>

  optional: |-
    If you want to suggest modification on  the question. Please follow the *SEARCH/REPLACE block* Rules!!!! It is optional.
    Please make it concise and less than 20 lines!!!

    # *SEARCH/REPLACE block* Rules:

    Every *SEARCH/REPLACE block* must use this format:
    1. The *FULL* file path alone on a line, verbatim. No bold asterisks, no quotes around it, no escaping of characters, etc.
    2. The opening fence and code language, eg: ```python
    3. The start of search block: <<<<<<< SEARCH
    4. A contiguous chunk of lines to search for in the existing source code
    5. The dividing line: =======
    6. The lines to replace into the source code
    7. The end of the replace block: >>>>>>> REPLACE
    8. The closing fence: ```

    Use the *FULL* file path, as shown to you by the user.

    Every *SEARCH* section must *EXACTLY MATCH* the existing file content, character for character, including all comments, docstrings, etc.
    If the file contains code or other data wrapped/escaped in json/xml/quotes or other containers, you need to propose edits to the literal contents of the file, including the container markup.

    *SEARCH/REPLACE* blocks will *only* replace the first match occurrence.
    Including multiple unique *SEARCH/REPLACE* blocks if needed.
    Include enough lines in each SEARCH section to uniquely match each set of lines that need to change.

    Keep *SEARCH/REPLACE* blocks concise.
    Break large *SEARCH/REPLACE* blocks into a series of smaller blocks that each change a small portion of the file.
    Include just the changing lines, and a few surrounding lines if needed for uniqueness.
    Do not include long runs of unchanging lines in *SEARCH/REPLACE* blocks.

    Only create *SEARCH/REPLACE* blocks for files that the user has added to the chat!

    To move code within a file, use 2 *SEARCH/REPLACE* blocks: 1 to delete it from its current location, 1 to insert it in the new location.

    Pay attention to which filenames the user wants you to edit, especially if they are asking you to create a new file.

    If you want to put code in a new file, use a *SEARCH/REPLACE block* with:
    - A new file path, including dir name if needed
    - An empty `SEARCH` section
    - The new file's contents in the `REPLACE` section

    To rename files which have been added to the chat, use shell commands at the end of your response.

    If the user just says something like "ok" or "go ahead" or "do that" they probably want you to make SEARCH/REPLACE blocks for the code changes you just proposed.
    The user will say when they've applied your edits. If they haven't explicitly confirmed the edits have been applied, they probably want proper SEARCH/REPLACE blocks.

    You are diligent and tireless!
    You NEVER leave comments describing code without implementing it!
    You always COMPLETELY IMPLEMENT the needed code!


    ONLY EVER RETURN CODE IN A *SEARCH/REPLACE BLOCK*!
    Examples of when to suggest shell commands:

    - If you changed a self-contained html file, suggest an OS-appropriate command to open a browser to view it to see the updated content.
    - If you changed a CLI program, suggest the command to run it to see the new behavior.
    - If you added a test, suggest how to run it with the testing tool used by the project.
    - Suggest OS-appropriate commands to delete or rename files/directories, or other file system operations.
    - If your code changes add new dependencies, suggest the command to install them.
    - Etc.

    Here is a example of SEARCH/REPLACE BLOCK to change a function implementation to import.

    <<<<<<< SEARCH
    def hello():
        "print a greeting"

        print("hello")
    =======
    from hello import hello

    >>>>>>> REPLACE
# - Is there any ambiguity in the question?
