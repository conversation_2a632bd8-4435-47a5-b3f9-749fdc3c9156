PythonAgentOut: |-
  The return code should be like
  ```Python
  <You code>
  ```

MarkdownOut: |-
  The return content should be like the format below(Please note tha "````" is used to avoid confliction of "```" in markdown file)
  ````markdown
  <the content of markdown file>
  ````

BatchEditOut: |-
  You should return an edition that applies to multiple files in a workspace in JSON.
  Except for the model file, other files should not be renamed.
  Files that do not need modifications should not be included in the returned text.

  For example:
  Inject the code into the folder. Your file name should always contain the suffix. Your file name keys should be unique to avoid delete or replace conflicts.
  {
      <file name1>: "<code>",  // indicate writing <code> into <file name1> (create a new file or update an existing file)
      {% if with_del %}
      <file name2>: "__DEL__"  // indicate removing file name2. When we want to just remove a file or replace a file to a new one, we usually use this
      {% else %}
      <file name2> (optional): "<code>"  // indicate writing <code> into <file name2> (create a new file or update an existing file)
      {% endif %}
  }

PythonBatchEditOut: |-
  You should return an edition that applies to multiple files in a workspace.
  Except for the model file, other files should not be renamed.
  Files that do not need modifications should not be included in the returned text.

  Response format should be like:
  ```<file name 1>
  <code>
  ```
  ```<file name 2>
  <code>
  ```
  {% if with_del %}
  ```<file name 3>
  __DEL__
  ```
  {% endif %}
  ...

  NOTE:
  - The file name should always contain the suffix.
  - The file name should be unique to prevent conflicts during removal or replacement.
  - To indicate writing code into a file, provide the corresponding code to replace "<code>" (creating a new file or updating an existing one).
  {% if with_del %}
  - To explicitly remove a file, provide only `__DEL__` within the code block for that file.
  - To replace a file with a new one, first provide ` __DEL__` for the original file, then include a separate entry with new file name and the new code.
  {% endif %}
