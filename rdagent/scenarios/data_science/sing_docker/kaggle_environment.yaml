name: kaggle
channels:
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.2.25=h06a4308_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - ncurses=6.4=h6a678d5_0
  - openssl=3.0.15=h5eee18b_0
  - pip=25.0=py311h06a4308_0
  - python=3.11.11=he870216_0
  - readline=8.2=h5eee18b_0
  - setuptools=75.8.0=py311h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - tk=8.6.14=h39e8969_0
  - wheel=0.45.1=py311h06a4308_0
  - xz=5.6.4=h5eee18b_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - absl-py==2.1.0
      - accelerate==0.33.0
      - aideml==0.1.4
      - aiohappyeyeballs==2.4.6
      - aiohttp==3.11.13
      - aiosignal==1.3.2
      - albucore==0.0.23
      - albumentations==1.4.14
      - alembic==1.14.1
      - annotated-types==0.7.0
      - anthropic==0.34.1
      - antlr4-python3-runtime==4.9.3
      - anyio==4.8.0
      - arrow==1.3.0
      - asttokens==3.0.0
      - astunparse==1.6.3
      - attrs==25.1.0
      - audioread==3.0.1
      - azure-ai-formrecognizer==3.3.3
      - azure-common==1.1.28
      - azure-core==1.32.0
      - azure-identity==1.20.0
      - azure-storage-blob==12.24.1
      - backoff==2.2.1
      - bayesian-optimization==1.5.1
      - bayespy==0.5.1
      - biopython==1.84
      - black==24.3.0
      - bleach==6.2.0
      - blis==0.7.11
      - brotli==1.1.0
      - bson==0.5.10
      - cachetools==5.5.2
      - catalogue==2.0.10
      - catboost==1.2.5
      - certifi==2025.1.31
      - cffi==1.17.1
      - charset-normalizer==3.4.1
      - click==8.1.8
      - cloudpathlib==0.20.0
      - cloudpickle==3.1.1
      - colorama==0.4.6
      - colorlog==6.9.0
      - comm==0.2.2
      - confection==0.1.5
      - contourpy==1.3.1
      - coolname==2.2.0
      - cryptography==44.0.2
      - cycler==0.12.1
      - cymem==2.0.11
      - cython==3.0.11
      - dacite==1.8.1
      - dataclasses-json==0.6.7
      - datasets==2.1.0
      - debugpy==1.8.12
      - decorator==5.2.1
      - defusedxml==0.7.1
      - dill==0.3.9
      - distro==1.9.0
      - efficientnet-pytorch==0.7.1
      - eval-type-backport==0.2.2
      - evaluate==0.4.2
      - executing==2.2.0
      - fastai==2.7.17
      - fastcore==1.7.29
      - fastdownload==0.0.7
      - fastdtw==0.3.4
      - fastjsonschema==2.21.1
      - fastprogress==1.0.3
      - faust-cchardet==2.1.19
      - filelock==3.17.0
      - flatbuffers==25.2.10
      - fonttools==4.56.0
      - frozenlist==1.5.0
      - fsspec==2025.2.0
      - funcy==2.0
      - future==1.0.0
      - gast==0.6.0
      - gdcm==1.1
      - gensim==4.3.3
      - genson==1.3.0
      - geographiclib==2.0
      - geopy==2.4.1
      - graphviz==0.20.3
      - greenlet==3.1.1
      - grpcio==1.71.0rc2
      - gym==0.26.2
      - gym-notices==0.0.8
      - h11==0.14.0
      - h5py==3.11.0
      - hmmlearn==0.3.2
      - httpcore==1.0.7
      - httplib2==0.22.0
      - httpx==0.27.2
      - huggingface-hub==0.29.1
      - humanize==4.8.0
      - hyperopt==0.2.7
      - idna==3.10
      - igraph==0.11.6
      - imagecodecs==2024.6.1
      - imageio==2.37.0
      - imbalanced-learn==0.12.3
      - imgaug==0.4.0
      - implicit==0.7.2
      - inflate64==1.0.1
      - iniconfig==2.0.0
      - ipykernel==6.29.5
      - ipython==8.27.0
      - isodate==0.7.2
      - jedi==0.19.2
      - jinja2==3.1.5
      - jiter==0.8.2
      - joblib==1.4.2
      - jsonlines==4.0.0
      - jsonpatch==1.33
      - jsonpointer==3.0.0
      - jsonschema==4.19.2
      - jsonschema-specifications==2024.10.1
      - jupyter-client==8.6.3
      - jupyter-core==5.7.2
      - kaggle==1.6.17
      - keras==3.5.0
      - kiwisolver==1.4.8
      - kornia==0.6.10
      - kornia-rs==0.1.8
      - langchain==0.2.15
      - langchain-anthropic==0.1.23
      - langchain-core==0.2.43
      - langchain-text-splitters==0.2.4
      - langcodes==3.5.0
      - langsmith==0.1.147
      - language-data==1.3.0
      - lazy-loader==0.4
      - levenshtein==0.25.1
      - libclang==18.1.1
      - librosa==0.10.2.post1
      - lightgbm==4.5.0
      - lightning-utilities==0.12.0
      - littleutils==0.2.4
      - llvmlite==0.43.0
      - loguru==0.7.2
      - lxml==5.3.1
      - mako==1.3.9
      - marisa-trie==1.2.1
      - markdown==3.7
      - markdown-it-py==3.0.0
      - markovify==0.9.4
      - markupsafe==3.0.2
      - marshmallow==3.26.1
      - matplotlib==3.9.2
      - matplotlib-inline==0.1.7
      - mdurl==0.1.2
      - ml-dtypes==0.4.1
      - mpmath==1.3.0
      - msal==1.31.1
      - msal-extensions==1.2.0
      - msgpack==1.1.0
      - msgpack-numpy==0.4.8
      - msrest==0.7.1
      - multidict==6.1.0
      - multiprocess==0.70.17
      - multivolumefile==0.2.3
      - munch==4.0.0
      - murmurhash==1.0.12
      - mypy-extensions==1.0.0
      - namex==0.0.8
      - nbformat==5.10.4
      - nest-asyncio==1.6.0
      - networkx==3.3
      - nltk==3.9.1
      - numba==0.60.0
      - numpy==1.26.2
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.1.105
      - nvidia-cuda-nvcc-cu12==12.3.107
      - nvidia-cuda-nvrtc-cu12==12.1.105
      - nvidia-cuda-runtime-cu12==12.1.105
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==*********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==**********
      - nvidia-cusparse-cu12==**********
      - nvidia-nccl-cu12==2.19.3
      - nvidia-nvjitlink-cu12==12.3.101
      - nvidia-nvtx-cu12==12.1.105
      - oauthlib==3.2.2
      - ogb==1.3.6
      - omegaconf==2.3.0
      - openai==1.48.0
      - opencv-python==*********
      - opencv-python-headless==*********
      - opt-einsum==3.4.0
      - optree==0.14.1
      - optuna==4.0.0
      - orjson==3.10.15
      - outdated==0.2.2
      - packaging==24.2
      - pandas==2.1.4
      - parso==0.8.4
      - pathspec==0.12.1
      - pdf2image==1.17.0
      - peft==0.12.0
      - pexpect==4.9.0
      - pillow==10.4.0
      - platformdirs==4.3.6
      - plotly==5.24.0
      - pluggy==1.5.0
      - pooch==1.8.2
      - portalocker==2.10.1
      - preshed==3.0.9
      - pretrainedmodels==0.7.4
      - prompt-toolkit==3.0.50
      - propcache==0.3.0
      - proto-plus==1.26.0
      - psutil==7.0.0
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - py4j==0.10.9.9
      - py7zr==0.22.0
      - pyaml==25.1.0
      - pyarrow==17.0.0
      - pyasn1==0.6.1
      - pyasn1-modules==0.4.1
      - pybcj==1.0.3
      - pycparser==2.22
      - pycryptodomex==3.21.0
      - pydantic==2.9.2
      - pydantic-core==2.23.4
      - pydantic-settings==2.6.1
      - pydicom==2.4.4
      - pygments==2.19.1
      - pyjwt==2.10.1
      - pylibjpeg==2.0.1
      - pyocr==0.8.5
      - pyparsing==3.1.4
      - pypdf==4.3.1
      - pyppmd==1.1.1
      - pytest==7.4.3
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.0.1
      - python-slugify==8.0.4
      - pytorch-lightning==2.4.0
      - pytz==2024.1
      - pyyaml==6.0.2
      - pyzmq==26.2.1
      - pyzstd==0.16.2
      - ranger21==0.1.0
      - rapidfuzz==3.12.2
      - referencing==0.36.2
      - regex==2024.11.6
      - requests==2.31.0
      - requests-oauthlib==2.0.0
      - requests-toolbelt==1.0.0
      - resampy==0.4.3
      - responses==0.18.0
      - rich==13.7.0
      - rouge-score==0.1.2
      - rpds-py==0.23.1
      - rsa==4.9
      - sacrebleu==2.4.3
      - safetensors==0.5.3
      - scikit-image==0.24.0
      - scikit-learn==1.2.2
      - scikit-optimize==0.10.2
      - scikit-surprise==1.1.4
      - scipy==1.11.4
      - seaborn==0.13.2
      - segmentation-models-pytorch==0.3.4
      - sentence-transformers==3.0.1
      - sentencepiece==0.2.0
      - shapely==2.0.7
      - shellingham==1.5.4
      - shutup==0.2.0
      - simsimd==6.2.1
      - six==1.17.0
      - sklearn-pandas==2.2.0
      - smart-open==7.1.0
      - sniffio==1.3.1
      - soundfile==0.13.1
      - soxr==0.5.0.post1
      - spacy==3.7.6
      - spacy-legacy==3.0.12
      - spacy-loggers==1.0.5
      - sqlalchemy==2.0.38
      - srsly==2.5.1
      - stack-data==0.6.3
      - stringzilla==3.12.2
      - sympy==1.13.2
      - tabulate==0.9.0
      - tenacity==8.5.0
      - tensorboard==2.17.1
      - tensorboard-data-server==0.7.2
      - tensorflow==2.17.0
      - tensorflow-hub==0.16.1
      - tensorflow-io-gcs-filesystem==0.37.1
      - tensorpack==0.11
      - termcolor==2.5.0
      - text-unidecode==1.3
      - textblob==0.18.0.post0
      - texttable==1.7.0
      - tf-keras==2.17.0
      - thinc==8.2.5
      - threadpoolctl==3.5.0
      - tifffile==2025.2.18
      - tiktoken==0.7.0
      - timm==0.9.7
      - tokenizers==0.19.1
      - torch==2.2.0
      - torch-geometric==2.3.1
      - torchaudio==2.2.0
      - torchdata==0.7.1
      - torchinfo==1.8.0
      - torchmetrics==1.3.1
      - torchtext==0.17.0
      - torchvision==0.17.0
      - tornado==6.4.2
      - tqdm==4.66.2
      - traitlets==5.14.3
      - transformers==4.44.2
      - triton==2.2.0
      - typer==0.15.2
      - types-python-dateutil==2.9.0.20241206
      - typing-extensions==4.12.2
      - typing-inspect==0.9.0
      - tzdata==2025.1
      - unidecode==1.3.8
      - uritemplate==4.1.1
      - urllib3==2.3.0
      - wasabi==1.1.3
      - wcwidth==0.2.13
      - weasel==0.4.1
      - webencodings==0.5.1
      - werkzeug==3.1.3
      - wrapt==1.17.2
      - xgboost==2.1.1
      - xlrd==2.0.1
      - xxhash==3.5.0
      - yarl==1.18.3
prefix: /opt/conda/envs/kaggle
