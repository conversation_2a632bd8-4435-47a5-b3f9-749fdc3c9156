dm_model_background: |-
  The model is a machine learning or deep learning structure used in clinical settings to predict whether the patient will suffer from acute respiratory failure (ARF) based on their vital signs monitored in ICU.
  The data is extracted from MIMIC-III using FIDDLE pipeline, and we focus on the ARF_12h prediction task, which means we use the first 12 hours data to predict the onset of ARF on discharge.
  The model is defined in the following parts:
  1. Name: The name of the model.
  2. Description: The description of the model.
  3. Architecture: The detailed architecture of the model, such as neural network layers or tree structures.
  The model should provide clear and detailed documentation of its architecture and hyperparameters. 

dm_model_interface: |-
  Your python code should follow the interface to better interact with the user's system.
  You code should contain several parts:
  1. The import part: import the necessary libraries.
  2. A class which is a sub-class of pytorch.nn.Module. This class should should have a init function and a forward function which inputs a tensor and outputs a tensor.
  3. Set a variable called "model_cls" to the class you defined.

  The user will save your code into a python file called "model.py". Then the user imports model_cls in file "model.py" after setting the cwd into the directory:
  ```python
  from model import model_cls
  ```
  So your python code should follow the pattern:
  ```python
  class XXXModel(torch.nn.Module):
      ...
  model_cls = XXXModel
  ```

  The model has one type, "TimeSeries". The input shape to a time series model is (batch_size, num_features, num_timesteps). The output shape of the model should be (batch_size, 1).
  The "batch_size" is a dynamic value which is determined by the input of forward function.
  The "num_features" and "num_timesteps" are static which will be provided to the model through init function.
  User will initialize the time series model with the following code:
  ```python
  model = model_cls(num_features=num_features, num_timesteps=num_timesteps)
  ```
  No other parameters will be passed to the model so give other parameters a default value or just make them static.

  The input tensor shape is (batch_size, num_features, num_timesteps) which is different from the normal time series input shape of (batch_size, num_timesteps, num_features). Please write code accordingly. 
  
  Note that for nn.Conv1d() layers, please do not permute the input tensor as the in_channel dimension should match the num_feature dimension.

  The output shape should be (batch_size, 1) with sigmoid activation since we have binary labels. 

  Don't write any try-except block in your python code. The user will catch the exception message and provide the feedback to you. Also, don't write main function in your python code. The user will call the forward method in the model_cls to get the output tensor.

  Please notice that your model should only use current features as input. The user will provide the input tensor to the model's forward function.


dm_model_output_format: |-
  Your output should be a tensor with shape (batch_size, 1). 
  The output tensor should be saved in a file named "output.pth" in the same directory as your python file.
  The user will evaluate the shape of the output tensor so the tensor read from "output.pth" should be 8 numbers.

dm_model_simulator: |-
  The models will be put to train on MIMIC-III dataset and evaluate their performance in terms of roc score (Area Under Curve Receiver Operating Characteristics Curve). Hypothesis is improved upon checking the feedback on the results. 