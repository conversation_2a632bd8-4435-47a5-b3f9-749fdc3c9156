# Motivation of the example
We use a runnable concrete example to demonstrate what the project should be like after being generated by a large language model.


# Content example and the workflow

> NOTE: the `README.md` itself is note generated by LLM. the content remains are generated by LLM.
>


## Extra input information beyond the competition information

[[../meta/spec.md]]
- [ ] TODO

## Step0: Specification generation

- Generate specification
  [[spec.md]]
  - [ ] TODO: perfect
- Generate loading data
  [[load_data.py]]

- Why do we merge this step together.
  - Successfully run `load_data.py` is a kind of verification of `spec.md`


## Step1: write the feature engineering code
- We can generate some file like [[feature.py]] that match the pattern `feat.*\.py`

## Step2: Model training


## Step3: ensemble and decision
- generate `ens_and_decsion`
  - why we generate score on ensemble phase
  - ensemble has following tasks which has great overlap 
    - ensemble usually check the performance before ensemble
    - A additional step to record performance is easier.

## Step4: Build workflow

[[main.py]]
