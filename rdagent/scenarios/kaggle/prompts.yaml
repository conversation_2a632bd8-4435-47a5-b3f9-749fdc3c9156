KG_hypothesis_gen_RAG: |-
  The user has proposed several hypothesis and conducted experiments to validate them. 
  The hypothesis can divided into two categories:
  1. Insights: These are the observations user did to other similar problems. You can either apply the same hypothesis or modify them to fit the current problem.
  2. Experience: These are former hypothesis and experiments user did to the current problem. You can either continue to improve the hypothesis or change to a new one.
  
  {% if insights %}
  The insights are as follows:
  {% for insight in insights %}
  Insight: {{ loop.index }}
  - hypothesis: {{ insight.hypothesis }}
  - experiments: {{ insight.experiments }}
  - conclusion: {{ insight.conclusion }}
  {% endfor %}
  {% endif %}

  {% if experiences %}
  The experiences are as follows:
  {% for experience in experiences %}
  Experience: {{ loop.index }}
  - hypothesis: {{ experience.hypothesis }}
  - experiments: {{ experience.experiments }}
  - conclusion: {{ experience.conclusion }}
  {% endfor %}
  {% endif %}

hypothesis_and_feedback: |-
  {% for experiment, feedback in trace.hist[-10:] %}
  Hypothesis {{ loop.index }}: {{ experiment.hypothesis }}
  Observation on the result with the hypothesis: {{ feedback.observations }}
  Feedback on the original hypothesis:  {{ feedback.hypothesis_evaluation }}
  Did changing to this hypothesis work? (focus on the change):  {{ feedback.decision }}
  {% endfor %}

hypothesis_output_format: |-
  The output should follow JSON format. The schema is as follows:
  {
    "action": "If "hypothesis_specification" provides the action you need to take, please follow "hypothesis_specification" to choose the action. Otherwise, based on previous experimental results, suggest the action you believe is most appropriate at the moment. It should be one of ["Feature engineering", "Feature processing", "Model feature selection", "Model tuning"]"
    "hypothesis": "The new hypothesis generated based on the information provided.",
    "reason": "The reason why you generate this hypothesis. It should be comprehensive and logical. It should cover the other keys below and extend them.",
    "concise_reason": "Two-line summary. First line focuses on a concise justification for the change. Second line generalizes a knowledge statement.",
    "concise_observation": "One line summary. It focuses on the observation of the given scenario, data characteristics, or previous experiences (failures & succeses).",
    "concise_justification": "One line summary. Justify the hypothesis based on theoretical principles or initial assumptions.",
    "concise_knowledge": "One line summary. Transferable knowledge based on theoretical principles. Use conditional grammar. eg. "If...., ..; When..., .; and etc" Make sure that you state things clearly without ambiguity. Eg. avoid saying "previous hypothesis", because one wouldn't know what that is."
  }

hypothesis_specification:
  Feature engineering: |-
    Action: Feature engineering
    
    Description: We engineer the features for the sake of best model performance on the basis of engineering the most influential features.
    
    1. Type of Feature and Data Characteristics:
      - Clearly define the type of feature being introduced.
      - Explain what data characteristics or patterns this feature captures.
      - Keep descriptions focused, avoiding redundant details to ensure clarity.

    2. Simple and Effective Features First:
      - Start by introducing features that are simple yet likely to be effective.
      - Provide a concise explanation of why these features are expected to perform well.
      - Avoid complex or combined features during the initial stages.
    
    3. Gradual Complexity Increase:
      - After initial feature testing, introduce more complex features.
      - Discuss both the potential benefits and any additional complexities of these features.
      - Begin combining features only after simpler ones have been tested and validated.

    4. New Directions and Optimizations:
      - If results suggest a need for a new approach, explain why, using data analysis, domain knowledge, or observed patterns.
      - Propose one new direction per iteration for clarity and focus.
      - If a previous hypothesis did not surpass the previous best but shows promise, continue in the same direction with optimizations.
      - Emphasize that features that outperform previous best results are added to the feature library, avoiding redundant work.
      
    5. 1-3 Feature Tasks per Generation:
      - Each generation should produce 1-3 feature tasks.
      - Maintain a balance between simplicity and complexity to develop a diverse and robust feature library.

  Feature processing: |-
    Action: Feature processing
    
    1. Feature Transformation and Normalization:
      - Clearly define any transformations applied to features (e.g., scaling, normalization, log transforms).
      - Explain how these transformations improve the data's suitability for the model.
      - Ensure transformations do not introduce unnecessary complexity early on.
    
    2. Handling Missing Values and Outliers:
      - Define any imputation methods used for missing data (e.g., mean, median, or more complex methods).
      - Explain how outliers are handled (e.g., clipping, removal, or transformation).
      - Ensure these processes are straightforward, enhancing data quality without overcomplicating early feature processing.
    
    3. Feature Interactions and Combinations:
      - After testing individual features, introduce combinations or interactions.
      - Discuss the potential advantages of feature interaction terms (e.g., polynomial or multiplicative features).
      - Ensure interactions are only applied after simpler, individual features have been processed.

    4. 1-3 Feature Tasks per Generation:
      - Each generation should produce 1-3 feature tasks.
      - Maintain a balance between simplicity and complexity to develop a diverse and robust feature library.

  Model feature selection: |-
    Action: Model feature selection

    1. Selection based on model_type:
      - Specify which features are being selected and explain why, considering the model type (e.g., NN, Random Forest, LightGBM, XGBoost).
      - Ensure the relationship between features and the model type is well-defined, as different features perform better on different models.
    
    2. Pattern recognition:
      - Explain the data characteristics or patterns that influenced feature selection for the specific model.
      - Clarify how the selected features complement the model's strengths and handle its potential weaknesses.

  Model tuning: |-
    Action: Model tuning
      
    1. Overview:
    - Clearly explain your hypothesis.
      - Which model are you tuning (one of the four types)?
      - How are you revising it, and why?
      - What are the innovations?
    - Base your hypothesis on previous structures and your understanding of the model code.
    - "Tuning" includes changing the model architecture or hyperparameters.

    2. Focus on Architecture and/or Hyperparameter Tuning:
      - Concentrate on designing new model architectures one at a time, hyperparameter tuning, or both.
      - Each hypothesis should introduce a novel architecture or a significant modification to an existing one.
      - Leverage prior experiences and hypothesis history.
      - If necessary, write source code manually to implement innovations beyond existing packages.

    3. Specific to Model Type:
      - Tuning must be specific to the model types available in our workspace (e.g., Neural Networks, XGBoost, Random Forest, LightGBM).
      - Clearly define the model type and the architecture or tuning being introduced.
      - Ensure the changes align with data characteristics and the model's strengths or limitations.

    4. Rationale Behind Architecture and Tuning:
      - Explain the reasoning behind your architectural design or tuning approach.
      - Justify how the new structure or parameter changes more effectively capture data patterns and improve learning efficiency.

feature_experiment_output_format: |-
  According to the hypothesis, please help user design one or more feature engineering tasks.
  The output should follow JSON format. The schema is as follows:
  {
      "factor or group name 1": {
          "description": "description of factor or group name 1",
          "formulation": "latex formulation of factor or group name 1",
          "variables": {
              "variable or function name 1": "description of variable or function 1",
              "variable or function name 2": "description of variable or function 2"
          }
      },
      "factor or group name 2": {
          "description": "description of factor or group name 2",
          "formulation": "latex formulation of factor or group name 2",
          "variables": {
              "variable or function name 1": "description of variable or function 1",
              "variable or function name 2": "description of variable or function 2"
          }
      }
      # Don't add ellipsis (...) or any filler text that might cause JSON parsing errors here!
  }

model_experiment_output_format: |-
  According to the hypothesis, please help user design one model task.
  We only build one model from four main model types: ["XGBoost", "RandomForest", "LightGBM", "NN"].
  The output should follow JSON format. The schema is as follows: 
  {
      "model_name": "model_name",
      "description": "A detailed description of the model",
      "architecture": "A detailed description of the model's architecture, e.g., neural network layers or tree structures",
      "hyperparameters": {
          "hyperparameter_name_1": "value of hyperparameter 1",
          "hyperparameter_name_2": "value of hyperparameter 2",
          "hyperparameter_name_3": "value of hyperparameter 3"
      },
      "model_type": "Please select only **one** model type from the following four options: XGBoost, RandomForest, LightGBM, or NN. The selected model must be unique and used as the **primary model**. You may choose an auxiliary model for support or optimization on specific tasks if necessary, but the primary model must come from the provided options."

  }

kg_feedback_generation_user: |-
  We are in a process of finding and validating hypotheses to build a powerful model. Each round aims to confirm or reject hypotheses based on results.

  The SOTA solution for the task is as follows:
  Features and its corresponding channel: {{ sota_features }}
  Models and its corresponding code: {{ sota_models }}
  Final result of the SOTA solution (we select the best-performing model's metric as the final result): {{ sota_result }}
  {% if sota_sub_results %}
  Sub-results of all sub-models: {{ sota_sub_results }}
  {% endif %}

  Current solution to be evaluated:
  Hypothesis: {{ current_hypothesis }}
  Reasoning: {{ current_hypothesis_reason }}
  Current target action: {{ current_target_action }}
  Experiments conducted and their code: {{ current_sub_exps_to_code }}
  Final result of the current solution (we select the best-performing model's metric as the final result): {{ current_result }}
  {% if current_sub_results %}
  Sub-results of all sub-models: {{ current_sub_results }}
  {% endif %}

  A more detailed comparison between the current solution and the SOTA solution:
  {{ combined_result }}

  Some information about comparing the current solution with the SOTA solution:
  {{ evaluation_description }}

  {% if last_hypothesis_and_feedback %}
  The user has made some hypothesis and conducted experiments to validate them, and the results are as follows:
  hypothesis: {{ last_hypothesis_and_feedback[0].hypothesis }}
  feedback decision: {{ last_hypothesis_and_feedback[1].decision }} 
  reason: {{ last_hypothesis_and_feedback[1].reason }}
  {% endif %}
  Please refer to these hypothesis and feedback to help you recommend new hypothesis

  Consider Changing Direction for Significant Gaps with the Best Result and the last round:
    - If the new results significantly differ from SOTA, consider a new direction.
    - If you've tweaked the same hyperparameter multiple times without improvement, it might be time to rethink or shift focus.
    - If it is model tuning, focus on comparing the SOTA's Sub-results of all sub-models: {{ sota_sub_results }} with the current experiment's Sub-results of all sub-models: {{ current_sub_results }}. For example, identify which model is currently the best, which model was adjusted in this experiment, and whether the adjustment was effective. Determine if there is potential to continue with this model or if another model shows more promise.

model_tuning_feedback_generation:
  system: |-
    You are an advanced assistant for analyzing results in data-driven R&D, in the context of designing machine learning models.
    The task is described in the following scenario:
    {{ scenario }}

    You will analyze the current experiment's hypothesis, model tuning code, results, and compare them with previous experiments and the best past result. 
    Your feedback should:
    1. Confirm if the current result supports or refutes the hypothesis.
    2. Compare with previous best results.
    3. Suggest improvements or new directions. Stay innovative and adaptive.

    Please provide detailed and constructive feedback. Note that as hypothesis evolve, a general trend should be that the model grows larger. 
    Example JSON Structure for Result Analysis:
    {
      "Observations": "Your overall observations here",
      "Feedback for Hypothesis": "Observations related to the hypothesis",
      "New Hypothesis": "Your new hypothesis here",
      "Reasoning": "Reasoning for the new hypothesis",
      "Replace Best Result": "yes or no"
    }

    Hypothesis Evolution Logic:
    - If the current hypothesis works, make the model more complex (e.g., add layers, neurons, etc.).
    - If a hypothesis works, build on it. If not, adjust at the same level before growing deeper. Think step by step and make changes. Act innovatively. 
    - If it doesn't, modify elements at the current level (e.g., adjust regularization, change features).

    Example Hypothesis Evolution Stages: (We want hypotheses to continue growing.) Levels include **Model Type**, **Layer Configuration**, **Activation Functions**, **Regularization Techniques**, **Feature Selection Methods**...
      - Initial Hypothesis: Use CNN with no feature selection.
      - Next Level (if successful): Add 5 convolutional layers, use all features.
      - Modify (if unsuccessful): Use 3 convolutional layers, add L1 regularization for feature selection.
      - Continue Growth (if successful): Add Leaky ReLU activation to all layers, retain L1-selected features.
      - Further Growth (if successful): Add dropout regularization (0.5 rate), retain L1 features.
      - Adjust (if unsuccessful): Use 5 layers, Leaky ReLU, dropout 0.3 rate.

factor_feedback_generation:
  system: |-
    You are a professional data feature engineering assistant in data-driven R&D. 
    The task is described in the following scenario:
    {{ scenario }}
    
    You will receive a hypothesis, multiple tasks with their features, their results, and the best previous result. 
    Your feedback should specify whether the current result supports or refutes the hypothesis, compare it with previous best results, and suggest improvements or new directions.
    
    Please understand the following operation logic and then make your feedback suitable for the scenario:
      1. Logic Explanation:
          - If the previous hypothesis feature surpasses the previous best, include this feature in the feature library.
          - New experiments will generate new features, which will be combined with the features in the library.
          - These combined features will be evaluated and compared against the current best to continuously iterate.
      2. Development Directions:
          - New Direction:
              - Propose a new feature direction for exploration and development.
          - Optimization of Existing Direction:
              - If the previous experiment's feature replaced the best, suggest further improvements to that feature.
              - Clearly specify the differences in name and improvements compared to the previous feature.
          - Continued Research:
              - If the previous experiment's feature did not replace the best, suggest ways to optimize and develop features in this direction.
      3. Final Goal:
          - The ultimate goal is to continuously accumulate features that surpass each iteration to maintain the best results.
    
    Consider Changing Direction for Significant Gaps with the Best Result:
      - If the new results significantly differ from the best result, consider exploring a new direction.
      - Avoid re-implementing previous features as those that surpassed the best are already included in the feature library and will be used in each run.
    Please provide detailed and constructive feedback for future exploration.
    Respond in JSON format. Example JSON structure for Result Analysis:
    {
      "Observations": "Your overall observations here",
      "Feedback for Hypothesis": "Observations related to the hypothesis",
      "New Hypothesis": "Your new hypothesis here",
      "Reasoning": "Reasoning for the new hypothesis",
      "Replace Best Result": "yes or no"
    }

feature_selection_feedback_generation:
  system: |-
    You are a professional feature selection assistant for machine learning models. Your task is to analyze the current feature selection strategy, evaluate its effectiveness, and suggest improvements.
    The task is described in the following scenario:
    {{ scenario }}
    
    In your feedback, consider:
    1. How effective is the current feature selection strategy?
    2. Are there any patterns in the selected or discarded features that might inform future selections?
    3. How might we refine or change the feature selection approach to improve model performance?
    4. Are there any domain-specific considerations that should inform our feature selection?

    Provide detailed and constructive feedback, focusing on actionable insights for feature selection improvement.
    
    Respond in JSON format. Example JSON structure for Result Analysis:
    {
      "Observations": "Your overall observations here",
      "Feedback for Hypothesis": "Observations related to the hypothesis",
      "New Hypothesis": "Your new hypothesis here",
      "Reasoning": "Reasoning for the new hypothesis",
      "Replace Best Result": "yes or no"
    }


model_feature_selection:
  system: |-
    You are an assistant for model feature selection in machine learning. Your task is to understand the current feature groups and choose the most relevant features for the model to get the best performance.

    The user is currently working on a Kaggle competition scenario as follows:
    {{ scenario }}

    The user is now working on the following model type:
    {{ model_type }}

    The user will give you several feature groups and their descriptions. Your task is to select the most relevant features for the model to achieve the best performance. You should consider the following:
    1. How well do the selected features support the scenario?
    2. Are there any features that might be redundant or noisy?

    Please answer the chosen group index in JSON format. Example JSON structure for Result Analysis:
    {
      "Selected Group Index": [1, 3, 5], # List of selected group indices, notice: the index starts from 1
    }

  user: |-
    Current feature groups:
    {% for feature in feature_groups %}
      Group {{ loop.index }}: 
      {{ feature }}
    {% endfor %}

gen_knowledge_from_code_mini_case:
  system: |-
    You were a proficient data scientist.
  user: |-
    The following notebook (contain markdown part and code part) is a high-performing solution for a kaggle competition.
    Please answer the following questions one by one and **as detailed as possible**.
    Make sure that another data scientist can exactly reproduce this copy of code based on your answer.
    Focus on the training process.

    (1) Please give a summary of the overall design.
    (2) What is the overall model architecture? Please use a long article to answer this question as accurately and in detail as possible.
    (3) How are the important hyper-parameters setting in this code?
    (4) What is the optimization objective?
    (5) What advanced machine learning technique does this copy of code use?
    (6) What other important tricks do you think play an important role for high performance?
    
    Note that make sure the answers are directly included from the code or markdown text, rather than based on your assumption.
    
    --------------------
    {{ notebook }}
    --------------------

gen_knowledge_from_code_RDAgent:
  system: |-
    You were a proficient data scientist.
  user: |-
    TODO...
