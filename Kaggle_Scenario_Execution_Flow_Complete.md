# 🔄 Kaggle 场景完整执行流程分析

## 📋 执行流程概览

Kaggle 场景的执行流程包含大量与大模型的交互，这些交互是整个系统智能决策的核心。本文档详细分析了代码的实际执行顺序，特别关注与 LLM 的交互部分。

### 🏗️ 整体架构
```
用户输入 → 初始化 → 循环执行 → LLM交互 → 代码生成 → 执行验证 → 反馈分析 → 下一轮循环
```

---

## 🚀 第一阶段：初始化阶段

### 1.1 入口点执行
**代码位置**: `rdagent/app/kaggle/loop.py:114-139`

```python
def main(path=None, step_n=None, competition=None):
    if competition:
        KAGGLE_IMPLEMENT_SETTING.competition = competition
        download_data(competition=competition, settings=KAGGLE_IMPLEMENT_SETTING)
        if KAGGLE_IMPLEMENT_SETTING.if_using_graph_rag:
            KAGGLE_IMPLEMENT_SETTING.knowledge_base = (
                "rdagent.scenarios.kaggle.knowledge_management.graph.KGKnowledgeGraph"
            )
    kaggle_loop = KaggleRDLoop(KAGGLE_IMPLEMENT_SETTING)
    kaggle_loop.run(step_n=step_n)
```

### 1.2 竞赛描述分析 (首次 LLM 交互)
**代码位置**: `rdagent/scenarios/kaggle/experiment/scenario.py:73-86`

```python
def _analysis_competition_description(self):
    sys_prompt = T(".prompts:kg_description_template.system").r()
    user_prompt = T(".prompts:kg_description_template.user").r(
        competition_descriptions=self.competition_descriptions,
        raw_data_information=self.source_data,
        evaluation_metric_direction=self.evaluation_metric_direction,
    )

    response_analysis = APIBackend().build_messages_and_create_chat_completion(
        user_prompt=user_prompt,
        system_prompt=sys_prompt,
        json_mode=True,
        json_target_type=Dict[str, str | bool | int],
    )
```

**LLM 交互目的**: 解析竞赛描述，提取结构化信息
- **输入**: 竞赛原始描述、数据信息、评估方向
- **输出**: JSON 格式的竞赛类型、描述、特征、评估指标等
- **模板位置**: `rdagent/scenarios/kaggle/experiment/prompts.yaml:1-26`

---

## 🔄 第二阶段：循环执行阶段

### 2.1 Step 1: 假设生成 (核心 LLM 交互)

#### 2.1.1 上下文准备
**代码位置**: `rdagent/scenarios/kaggle/proposal/proposal.py:250-289`

```python
def prepare_context(self, trace: Trace) -> Tuple[dict, bool]:
    hypothesis_and_feedback = (
        T("scenarios.kaggle.prompts:hypothesis_and_feedback").r(trace=trace,)
        if len(trace.hist) > 0
        else "No previous hypothesis and feedback available since it's the first round."
    )

    if self.scen.if_action_choosing_based_on_UCB:
        action = self.execute_next_action(trace)

    context_dict = {
        "hypothesis_and_feedback": hypothesis_and_feedback,
        "RAG": generate_RAG_content(scen=self.scen, trace=trace, hypothesis_and_feedback=hypothesis_and_feedback, target=action),
        "hypothesis_output_format": T("scenarios.kaggle.prompts:hypothesis_output_format").r(),
        "hypothesis_specification": hypothesis_specification,
    }
    return context_dict, True
```

#### 2.1.2 RAG 知识检索 (可选 LLM 交互)
**代码位置**: `rdagent/scenarios/kaggle/knowledge_management/vector_base.py:270-280`

```python
def refine_with_LLM(self, target: str, text: str) -> str:
    sys_prompt = T(".prompts:refine_with_LLM.system").r()
    user_prompt = T(".prompts:refine_with_LLM.user").r(target=target, text=text)

    response = APIBackend().build_messages_and_create_chat_completion(
        user_prompt=user_prompt,
        system_prompt=sys_prompt,
        json_mode=False,
    )
    return response
```

#### 2.1.3 假设生成 LLM 调用
**代码位置**: `rdagent/components/proposal/base.py` (基类实现)

**LLM 交互目的**: 基于历史反馈和知识生成新假设
- **输入**: 历史假设反馈、RAG 检索结果、动作规范
- **输出**: JSON 格式的新假设，包含动作类型、假设内容、推理过程
- **模板位置**: `rdagent/scenarios/kaggle/prompts.yaml:35-45`

### 2.2 Step 2: 实验生成 (LLM 交互)

#### 2.2.1 实验转换准备
**代码位置**: `rdagent/scenarios/kaggle/proposal/proposal.py:308-346`

```python
def prepare_context(self, hypothesis: Hypothesis, trace: Trace) -> Tuple[dict, bool]:
    scenario = trace.scen.get_scenario_all_desc(filtered_tag="hypothesis_and_experiment")
    experiment_output_format = (
        T("scenarios.kaggle.prompts:feature_experiment_output_format").r()
        if hypothesis.action in [KG_ACTION_FEATURE_ENGINEERING, KG_ACTION_FEATURE_PROCESSING]
        else T("scenarios.kaggle.prompts:model_experiment_output_format").r()
    )

    return {
        "target_hypothesis": str(hypothesis),
        "scenario": scenario,
        "hypothesis_and_feedback": hypothesis_and_feedback,
        "experiment_output_format": experiment_output_format,
        "RAG": generate_RAG_content(trace.scen, trace, hypothesis_and_feedback, chosen_hypothesis=hypothesis.hypothesis),
    }, True
```

**LLM 交互目的**: 将假设转换为具体的实验任务
- **输入**: 假设内容、场景描述、历史反馈
- **输出**: 特征工程任务或模型调优任务的详细规范

### 2.3 Step 3: 代码生成 (多次 LLM 交互)

#### 2.3.1 特征编码器 LLM 交互
**代码位置**: `rdagent/scenarios/kaggle/developer/coder.py` (KGFactorCoSTEER)

**LLM 交互目的**: 生成特征工程代码
- **输入**: 特征工程任务规范、数据描述、接口要求
- **输出**: Python 特征工程类代码
- **模板位置**: `rdagent/scenarios/kaggle/experiment/prompts.yaml:99-118`

#### 2.3.2 模型编码器 LLM 交互
**代码位置**: `rdagent/scenarios/kaggle/developer/coder.py` (KGModelCoSTEER)

**LLM 交互目的**: 生成模型训练代码
- **输入**: 模型任务规范、数据描述、模型类型
- **输出**: 模型训练和预测代码

#### 2.3.3 模型特征选择 LLM 交互
**代码位置**: `rdagent/scenarios/kaggle/developer/coder.py:54-61`

```python
chosen_index = json.loads(
    APIBackend().build_messages_and_create_chat_completion(
        user_prompt=user_prompt,
        system_prompt=system_prompt,
        json_mode=True,
        json_target_type=Dict[str, List[int]],
    )
).get("Selected Group Index", [i + 1 for i in range(len(exp.experiment_workspace.data_description))])
```

**LLM 交互目的**: 智能选择特征子集
- **输入**: 可用特征列表、模型类型、任务描述
- **输出**: JSON 格式的特征索引列表

### 2.4 Step 4: 代码执行 (无 LLM 交互)

代码在 Docker 环境中执行，收集性能指标和错误信息。

### 2.5 Step 5: 反馈生成 (重要 LLM 交互)

#### 2.5.1 反馈生成准备
**代码位置**: `rdagent/scenarios/kaggle/developer/feedback.py:70-80`

```python
# Generate the user prompt based on the action type
if hypothesis.action == "Model tuning":
    prompt_key = "model_tuning_feedback_generation"
elif hypothesis.action == "Model feature selection":
    prompt_key = "model_feature_selection_feedback_generation"
else:  # Feature engineering or processing
    prompt_key = "factor_feedback_generation"

sys_prompt = T(f"scenarios.kaggle.prompts:{prompt_key}.system").r(scenario=self.scen.get_scenario_all_desc())
user_prompt = T(f"scenarios.kaggle.prompts:{prompt_key}.user").r(
    current_hypothesis=hypothesis.hypothesis,
    current_result=current_result,
    sota_result=sota_result,
    # ... 其他参数
)
```

#### 2.5.2 反馈生成 LLM 调用
**LLM 交互目的**: 分析实验结果并生成改进建议
- **输入**: 当前假设、实验结果、SOTA 结果、历史反馈
- **输出**: 结构化反馈，包含假设评估、决策、改进建议
- **模板位置**: `rdagent/scenarios/kaggle/prompts.yaml:217-260`

---

## 🔧 LLM 交互基础设施

### 3.1 APIBackend 架构
**代码位置**: `rdagent/oai/backend/base.py:181-400`

```python
class APIBackend(ABC):
    def build_messages_and_create_chat_completion(
        self,
        user_prompt: str,
        system_prompt: str = None,
        json_mode: bool = False,
        json_target_type: type = None,
        **kwargs
    ) -> str:
        # 构建消息并调用 LLM
        messages = self._build_messages(user_prompt, system_prompt)
        return self._try_create_chat_completion_or_embedding(
            messages=messages,
            json_mode=json_mode,
            chat_completion=True,
            **kwargs
        )
```

### 3.2 模板系统
**代码位置**: `rdagent/utils/agent/tpl.py:73-136`

```python
class RDAT:
    def __init__(self, uri: str, ftype: str = "yaml"):
        self.uri = uri
        self.template = load_content(uri, caller_dir=caller_dir, ftype=ftype)

    def r(self, **context: Any) -> str:
        rendered = (
            Environment(undefined=StrictUndefined, loader=FunctionLoader(load_content))
            .from_string(self.template)
            .render(**context)
            .strip("\n")
        )
        return rendered
```

### 3.3 缓存机制
**代码位置**: `rdagent/oai/backend/base.py:68-88`

```python
def chat_get(self, key: str) -> str | None:
    md5_key = md5_hash(key)
    self.c.execute("SELECT chat FROM chat_cache WHERE md5_key=?", (md5_key,))
    result = self.c.fetchone()
    return None if result is None else result[0]

def chat_set(self, key: str, value: str) -> None:
    md5_key = md5_hash(key)
    self.c.execute(
        "INSERT OR REPLACE INTO chat_cache (md5_key, chat) VALUES (?, ?)",
        (md5_key, value),
    )
    self.conn.commit()
```

---

## 📊 LLM 交互时序图

```mermaid
sequenceDiagram
    participant User
    participant KaggleLoop
    participant LLM
    participant Docker
    
    User->>KaggleLoop: 启动竞赛
    KaggleLoop->>LLM: 分析竞赛描述
    LLM-->>KaggleLoop: 结构化竞赛信息
    
    loop 每个循环迭代
        KaggleLoop->>LLM: 生成假设 (基于历史+RAG)
        LLM-->>KaggleLoop: 新假设+动作类型
        
        KaggleLoop->>LLM: 转换为实验任务
        LLM-->>KaggleLoop: 实验规范
        
        KaggleLoop->>LLM: 生成代码 (特征/模型)
        LLM-->>KaggleLoop: Python 代码
        
        KaggleLoop->>Docker: 执行代码
        Docker-->>KaggleLoop: 执行结果
        
        KaggleLoop->>LLM: 分析结果生成反馈
        LLM-->>KaggleLoop: 结构化反馈
        
        KaggleLoop->>KaggleLoop: 更新 UCB 参数
    end
```

---

## 🎯 关键 LLM 交互点总结

| 阶段 | 交互次数 | 主要目的 | 输入类型 | 输出格式 |
|------|----------|----------|----------|----------|
| **初始化** | 1次 | 竞赛分析 | 文本描述 | JSON |
| **假设生成** | 1次 | 策略决策 | 历史+RAG | JSON |
| **实验转换** | 1次 | 任务规范 | 假设+场景 | JSON |
| **代码生成** | 2-3次 | 代码实现 | 任务规范 | Python代码 |
| **反馈分析** | 1次 | 结果评估 | 结果+历史 | 结构化反馈 |

**每个循环总计**: 5-7次 LLM 交互
**特殊交互**: RAG 知识精炼、知识提取等

---

## 📁 关键文件位置汇总

### 核心执行流程
- `rdagent/app/kaggle/loop.py` - 主循环控制
- `rdagent/scenarios/kaggle/experiment/scenario.py` - 场景管理
- `rdagent/scenarios/kaggle/proposal/proposal.py` - 假设生成

### LLM 交互基础设施
- `rdagent/oai/backend/base.py` - API 后端基类
- `rdagent/oai/backend/litellm.py` - LiteLLM 实现
- `rdagent/utils/agent/tpl.py` - 模板系统

### 提示模板
- `rdagent/scenarios/kaggle/prompts.yaml` - Kaggle 专用提示
- `rdagent/scenarios/kaggle/experiment/prompts.yaml` - 实验相关提示
- `rdagent/scenarios/kaggle/knowledge_management/prompts.yaml` - 知识管理提示

### 编码器和运行器
- `rdagent/scenarios/kaggle/developer/coder.py` - 代码生成器
- `rdagent/scenarios/kaggle/developer/runner.py` - 代码执行器
- `rdagent/scenarios/kaggle/developer/feedback.py` - 反馈生成器

---

## 🔍 详细 LLM 交互分析

### 4.1 竞赛描述分析详解

**提示模板结构** (`rdagent/scenarios/kaggle/experiment/prompts.yaml:1-26`):
```yaml
kg_description_template:
  system: |-
    You are an assistant that extracts structured information from unstructured text.
    The user will provide you a Kaggle competition description, and you need to extract specific details from it.
    Please answer in Json format with the following schema:
    {
      "Competition Type": "The type of competition, e.g., 'Classification', 'Regression'",
      "Competition Description": "A brief description of the competition",
      "Target Description": "Description of what needs to be predicted",
      "Competition Features": "List of important features in the dataset",
      "Submission Specifications": "Requirements for submission format",
      "Metric Evaluation Description": "Description of evaluation metrics"
    }
  user: |-
    Competition Description: {{ competition_descriptions }}
    The raw data information: {{ raw_data_information }}
    Evaluation_metric_direction: {{ evaluation_metric_direction }}
```

**交互流程**:
1. **输入构建**: 合并竞赛描述、数据信息、评估方向
2. **LLM 调用**: JSON 模式确保结构化输出
3. **结果解析**: 提取竞赛类型、特征、评估指标等
4. **状态更新**: 更新场景对象的属性

### 4.2 假设生成详解

**核心提示模板** (`rdagent/scenarios/kaggle/prompts.yaml:35-45`):
```yaml
hypothesis_output_format: |-
  The output should follow JSON format. The schema is as follows:
  {
    "action": "Choose from ['Feature engineering', 'Feature processing', 'Model feature selection', 'Model tuning']",
    "hypothesis": "The new hypothesis generated based on the information provided.",
    "reason": "Comprehensive and logical reasoning for this hypothesis.",
    "concise_reason": "Brief summary of the reasoning.",
    "concise_observation": "Key observations from previous experiments.",
    "concise_justification": "Why this approach should work.",
    "concise_knowledge": "Relevant domain knowledge applied."
  }
```

**UCB 动作选择算法** (`rdagent/scenarios/kaggle/proposal/proposal.py:228-248`):
```python
def execute_next_action(self, trace: Trace) -> str:
    actions = list(self.scen.action_counts.keys())
    t = sum(self.scen.action_counts.values()) + 1

    # 优先探索未尝试的动作
    for action in actions:
        if self.scen.action_counts[action] == 0:
            return action

    # 计算 UCB 值
    c = self.scen.confidence_parameter
    ucb_values = {}
    for action in actions:
        mu_o = self.scen.reward_estimates[action]
        n_o = self.scen.action_counts[action]
        ucb = mu_o + c * math.sqrt(math.log(t) / n_o)
        ucb_values[action] = ucb

    return max(ucb_values, key=ucb_values.get)
```

### 4.3 RAG 知识检索详解

**向量 RAG** (`rdagent/scenarios/kaggle/proposal/proposal.py:66-71`):
```python
if scen.if_using_vector_rag:
    if scen.mini_case:
        rag_results, _ = scen.vector_base.search_experience(target, hypothesis_and_feedback, topk_k=1)
    else:
        rag_results, _ = scen.vector_base.search_experience(target, hypothesis_and_feedback, topk_k=5)
    return "\n".join([doc.content for doc in rag_results])
```

**图 RAG** (`rdagent/scenarios/kaggle/proposal/proposal.py:142-155`):
```python
similar_nodes = trace.knowledge_base.semantic_search(
    node=trace.scen.get_competition_full_desc(),
    topk_k=2,
)

for similar_node in similar_nodes:
    for hypothesis_type in KG_ACTION_LIST:
        hypothesis_nodes = trace.knowledge_base.get_nodes_within_steps(
            start_node=similar_node,
            steps=3,
            constraint_labels=[hypothesis_type],
        )
        found_nodes.extend(hypothesis_nodes[:2])
```

### 4.4 代码生成详解

**特征工程代码模板** (`rdagent/scenarios/kaggle/experiment/prompts.yaml:99-118`):
```yaml
kg_feature_interface: |-
  class FeatureEngineeringName:
      def fit(self, train_df: pd.DataFrame):
          """
          Fit the feature engineering model to the training data.
          """
          return self

      def transform(self, X: pd.DataFrame):
          """
          Transform the input data.
          """
          return X  # 返回处理后的特征

  feature_engineering_cls = FeatureEngineeringName
```

**模型选择 LLM 交互** (`rdagent/scenarios/kaggle/developer/coder.py:54-61`):
```python
chosen_index = json.loads(
    APIBackend().build_messages_and_create_chat_completion(
        user_prompt=user_prompt,
        system_prompt=system_prompt,
        json_mode=True,
        json_target_type=Dict[str, List[int]],
    )
).get("Selected Group Index", [i + 1 for i in range(len(exp.experiment_workspace.data_description))])
```

### 4.5 反馈生成详解

**反馈模板选择逻辑** (`rdagent/scenarios/kaggle/developer/feedback.py:70-80`):
```python
if hypothesis.action == "Model tuning":
    prompt_key = "model_tuning_feedback_generation"
elif hypothesis.action == "Model feature selection":
    prompt_key = "model_feature_selection_feedback_generation"
else:  # Feature engineering or processing
    prompt_key = "factor_feedback_generation"
```

**模型调优反馈模板** (`rdagent/scenarios/kaggle/prompts.yaml:217-227`):
```yaml
model_tuning_feedback_generation:
  system: |-
    You are an advanced assistant for analyzing results in data-driven R&D.
    The task is described in the following scenario: {{ scenario }}

    Your feedback should:
    1. Confirm if the current result supports or refutes the hypothesis.
    2. Compare with previous best results.
    3. Suggest improvements or new directions. Stay innovative and adaptive.
```

---

## 🎛️ LLM 配置和优化

### 5.1 LiteLLM 后端配置
**代码位置**: `rdagent/oai/backend/litellm.py:79-98`

```python
def _create_chat_completion_inner_function(
    self,
    messages: list[dict[str, Any]],
    json_mode: bool = False,
    *args,
    **kwargs,
) -> tuple[str, str | None]:
    if json_mode and supports_response_schema(model=LITELLM_SETTINGS.chat_model):
        kwargs["response_format"] = {"type": "json_object"}

    logger.info(self._build_log_messages(messages), tag="llm_messages")
    model = LITELLM_SETTINGS.chat_model
    temperature = LITELLM_SETTINGS.chat_temperature
    max_tokens = LITELLM_SETTINGS.chat_max_tokens
    reasoning_effort = LITELLM_SETTINGS.reasoning_effort
```

### 5.2 重试和错误处理
**代码位置**: `rdagent/oai/backend/base.py:326-345`

```python
def _try_create_chat_completion_or_embedding(
    self,
    max_retry: int = 10,
    chat_completion: bool = False,
    *args,
    **kwargs,
) -> str | list[list[float]]:
    max_retry = LLM_SETTINGS.max_retry if LLM_SETTINGS.max_retry is not None else max_retry
    timeout_count = 0
    violation_count = 0
    for i in range(max_retry):
        try:
            if chat_completion:
                return self._create_chat_completion_auto_continue(*args, **kwargs)
        except Exception as e:
            logger.warning(f"API call failed (attempt {i+1}/{max_retry}): {e}")
            # 重试逻辑
```

### 5.3 缓存优化
**代码位置**: `rdagent/oai/backend/base.py:68-88`

```python
class ChatCache:
    def chat_get(self, key: str) -> str | None:
        md5_key = md5_hash(key)
        self.c.execute("SELECT chat FROM chat_cache WHERE md5_key=?", (md5_key,))
        result = self.c.fetchone()
        return None if result is None else result[0]

    def chat_set(self, key: str, value: str) -> None:
        md5_key = md5_hash(key)
        self.c.execute(
            "INSERT OR REPLACE INTO chat_cache (md5_key, chat) VALUES (?, ?)",
            (md5_key, value),
        )
        self.conn.commit()
```

---

## 📈 性能监控和日志

### 6.1 LLM 调用日志
**代码位置**: `rdagent/oai/backend/litellm.py:93`

```python
logger.info(self._build_log_messages(messages), tag="llm_messages")
```

### 6.2 模板渲染日志
**代码位置**: `rdagent/utils/agent/tpl.py:124-133`

```python
logger.log_object(
    obj={
        "uri": self.uri,
        "template": self.template,
        "context": context,
        "rendered": rendered,
    },
    tag="debug_tpl",
)
```

### 6.3 会话管理
**代码位置**: `rdagent/oai/backend/base.py:148-171`

```python
def build_chat_completion(self, user_prompt: str, *args, **kwargs) -> str:
    messages = self.build_chat_completion_message(user_prompt)

    with logger.tag(f"session_{self.conversation_id}"):
        response: str = self.api_backend._try_create_chat_completion_or_embedding(
            *args,
            messages=messages,
            chat_completion=True,
            **kwargs,
        )
        logger.log_object({"user": user_prompt, "resp": response}, tag="debug_llm")

    messages.append({"role": "assistant", "content": response})
    SessionChatHistoryCache().message_set(self.conversation_id, messages)
    return response
```

---

## 🔧 故障排除和调试

### 7.1 常见 LLM 交互问题

1. **JSON 解析失败**
   - **位置**: `rdagent/scenarios/kaggle/knowledge_management/extract_knowledge.py:18-22`
   - **处理**: 捕获 JSONDecodeError，返回错误信息

2. **API 调用超时**
   - **位置**: `rdagent/oai/backend/base.py:337-345`
   - **处理**: 自动重试机制，最多重试 10 次

3. **模板渲染错误**
   - **位置**: `rdagent/utils/agent/tpl.py:116-121`
   - **处理**: StrictUndefined 确保模板变量完整

### 7.2 调试工具

1. **LLM 消息日志**: `tag="llm_messages"`
2. **模板调试日志**: `tag="debug_tpl"`
3. **会话调试日志**: `tag="debug_llm"`

---

## 🎯 总结

这个完整的执行流程展示了 Kaggle 场景如何通过精心设计的 LLM 交互链，实现智能的竞赛解决方案迭代优化：

1. **结构化分析**: 将非结构化竞赛描述转换为结构化信息
2. **智能决策**: 通过 UCB 算法和 RAG 检索做出最优动作选择
3. **代码生成**: 将高级策略转换为可执行的 Python 代码
4. **结果分析**: 深度分析实验结果并提供改进建议
5. **知识积累**: 持续学习和优化决策策略

每个 LLM 交互都经过精心设计，确保输入输出的质量和一致性，从而实现高效的自动化机器学习工程。
