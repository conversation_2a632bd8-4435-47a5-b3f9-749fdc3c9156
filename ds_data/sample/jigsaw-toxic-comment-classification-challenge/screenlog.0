(base) [01;32msunhl@AGX-18[00m:[01;34m~/fangye/RD-Agent/ds_data/jigsaw-toxic-comment-classification-challenge[00m$ conda actiate [K[K[K[Ka[Kvate [K[K[K[K[K[K[K[K[Kinfo --env

# conda environments:
#
base                 * /home/<USER>/miniforge3
football               /home/<USER>/miniforge3/envs/football
load_predict           /home/<USER>/miniforge3/envs/load_predict
openhands              /home/<USER>/miniforge3/envs/openhands
rdagent                /home/<USER>/miniforge3/envs/rdagent

(base) [01;32msunhl@AGX-18[00m:[01;34m~/fangye/RD-Agent/ds_data/jigsaw-toxic-comment-classification-challenge[00m$ o[Kconda activatge [K[K[Ke rdagent
(rdagent) [01;32msunhl@AGX-18[00m:[01;34m~/fangye/RD-Agent/ds_data/jigsaw-toxic-comment-classification-challenge[00m$ cd ../../
(rdagent) [01;32msunhl@AGX-18[00m:[01;34m~/fangye/RD-Agent[00m$ rdagent data_science --cpm[K[Kompt[Ketition jigsaw-toxic-comment-classification-challenge -t 43200
[32m2025-06-24 20:58:50.385[0m | [1mINFO    [0m | [36mrdagent.oai.backend.litellm[0m:[36m<module>[0m:[36m41[0m - [1mbackend='rdagent.oai.backend.LiteLLMAPIBackend' chat_model='gpt-4o' embedding_model='text-embedding-3-small' reasoning_effort=None reasoning_think_rm=False log_llm_chat_content=True use_azure=False chat_use_azure=False embedding_use_azure=False chat_use_azure_token_provider=False embedding_use_azure_token_provider=False managed_identity_client_id=None max_retry=10 retry_wait_seconds=1 dump_chat_cache=False use_chat_cache=False dump_embedding_cache=False use_embedding_cache=False prompt_cache_path='/home/<USER>/fangye/RD-Agent/prompt_cache.db' max_past_message_include=10 timeout_fail_limit=10 violation_fail_limit=1 use_auto_chat_cache_seed_gen=False init_chat_cache_seed=42 openai_api_key='sk-9UfRPctnewUsXxp9ARQt15yC6Zayv6krtFQsftJ9mtomARR4vBhr' chat_openai_api_key=None chat_openai_base_url=None chat_azure_api_base='' chat_azure_api_version='' chat_max_tokens=None chat_temperature=0.5 chat_stream=True chat_seed=None chat_frequency_penalty=0.0 chat_presence_penalty=0.0 chat_token_limit=100000 default_system_prompt="You are an AI assistant who helps to answer user's questions." system_prompt_role='system' embedding_openai_api_key='' embedding_openai_base_url='' embedding_azure_api_base='' embedding_azure_api_version='' embedding_max_str_num=50 use_llama2=False llama2_ckpt_dir='Llama-2-7b-chat' llama2_tokenizer_path='Llama-2-7b-chat/tokenizer.model' llams2_max_batch_size=8 use_gcr_endpoint=False gcr_endpoint_type='llama2_70b' llama2_70b_endpoint='' llama2_70b_endpoint_key='' llama2_70b_endpoint_deployment='' llama3_70b_endpoint='' llama3_70b_endpoint_key='' llama3_70b_endpoint_deployment='' phi2_endpoint='' phi2_endpoint_key='' phi2_endpoint_deployment='' phi3_4k_endpoint='' phi3_4k_endpoint_key='' phi3_4k_endpoint_deployment='' phi3_128k_endpoint='' phi3_128k_endpoint_key='' phi3_128k_endpoint_deployment='' gcr_endpoint_temperature=0.7 gcr_endpoint_top_p=0.9 gcr_endpoint_do_sample=False gcr_endpoint_max_token=100 chat_use_azure_deepseek=False chat_azure_deepseek_endpoint='' chat_azure_deepseek_key='' chat_model_map={}[0m
[INFO] Original dataset folder `/home/<USER>/fangye/RD-Agent/ds_data/jigsaw-toxic-comment-classification-challenge` has 6 files in total (including subfolders).
File type counts:
.csv: 4
.md: 1
.0: 1

Processing data:   0%|                                                                                                         | 0/6 [00:00<?, ?file/s]
Processing data:  17%|████████████████▏                                                                                | 1/6 [00:00<00:00,  6.11file/s]
Processing data:  33%|████████████████████████████████▎                                                                | 2/6 [00:01<00:02,  1.66file/s]
Processing data:  50%|████████████████████████████████████████████████▌                                                | 3/6 [00:01<00:01,  2.55file/s]
Processing data:  67%|████████████████████████████████████████████████████████████████▋                                | 4/6 [00:02<00:01,  1.79file/s]
Processing data: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████| 6/6 [00:02<00:00,  2.96file/s]

Processing files:   0%|                                                                                                        | 0/2 [00:00<?, ?file/s]Sampling 1 files without label from 1 files in description.md
Copying 0 extra files
Sampling 1 files without label