# Detecting Insults in Social Commentary

## 🧠 Competition Objective

The goal of this competition is to build a binary classifier that detects whether a comment in a public discussion is **insulting** (1) or **not insulting** (0). This can be useful for content moderation, fostering respectful discourse, and mitigating online toxicity.

---

## 📂 Dataset Description

The dataset is divided into the following files:

### 1. `train.csv`
- **Samples**: 3947
- **Columns**:
  - `Insult`: Label indicating if the comment is insulting (1 = Yes, 0 = No)
  - `Date`: Timestamp of the comment (some missing)
  - `Comment`: Raw text of the social commentary
- **Label distribution**:
  - 0 (Not Insult): 2898
  - 1 (Insult): 1049

### 2. `test.csv`
- **Samples**: 2235
- **Columns**:
  - `Date`: Timestamp (optional use)
  - `Comment`: Raw text of the comment
- **Labels**: Not provided; to be predicted

### 3. `test_with_solutions.csv`
- Same structure as `test.csv`, but includes the true `Insult` labels for evaluation purposes.

### 4. `impermium_verification_set.csv` and `impermium_verification_labels.csv`
- An extended validation set.
- Useful for additional model tuning and verification beyond the official test set.

---

## 📊 Evaluation Metric

The performance is primarily evaluated using **Area Under the ROC Curve (AUC)** or **Accuracy**, depending on the implementation context.

---

## ✅ Suggested Modeling Pipeline

1. **Preprocessing**:
   - Clean and normalize text (lowercase, remove HTML, punctuation, etc.)
   - Remove or convert special characters and encodings

2. **Feature Engineering**:
   - TF-IDF on word and character n-grams
   - Presence of offensive keywords
   - Capital letter ratio, exclamation count, etc.

3. **Modeling**:
   - Logistic Regression, SVM, Random Forest, or Gradient Boosting
   - Ensemble learning can boost performance

4. **Validation**:
   - Use `impermium_verification_set.csv` for external validation
   - Optionally use k-fold cross-validation on `train.csv`

---

## 📁 Submission Format

Submit predictions in a format matching `sample_submission_null.csv`:
```
Id,Insult
1,0
2,1
...
```

---

## 📌 Notes

- The `Date` field is optional and not always present.
- Be mindful of text encoding issues (e.g., `\xc2\xa0`, `\u011b`) that require decoding before modeling.

