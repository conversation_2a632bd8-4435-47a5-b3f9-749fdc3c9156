{"Description": "<p>Kaggle competitions are incredibly fun and rewarding, but they can also be intimidating for people who are relatively new in their data science journey. In the past, we've launched many Playground competitions that are more approachable than our Featured competitions and thus, more beginner-friendly. </p>\n<p>In order to have a more consistent offering of these competitions for our community, we're trying a new experiment in 2021. We'll be launching month-long tabular Playground competitions on the 1st of every month and continue the experiment as long as there's sufficient interest and participation.</p>\n<p>The goal of these competitions is to provide a fun, and approachable for anyone, tabular dataset. These competitions will be great for people looking for something in between the Titanic Getting Started competition and a Featured competition. If you're an established competitions master or grandmaster, these probably won't be much of a challenge for you. We encourage you to avoid saturating the leaderboard.</p>\n<p>For each monthly competition, we'll be offering Kaggle Merchandise for the top three teams. And finally, because we want these competitions to be more about learning, we're limiting team sizes to 3 individuals. </p>\n<p>The dataset is used for this competition is synthetic, but based on a real dataset and generated using a <a rel=\"noreferrer nofollow\" aria-label=\"CTGAN (opens in a new tab)\" target=\"_blank\" href=\"https://github.com/sdv-dev/CTGAN\">CTGAN</a>. This dataset is based off of the original <a target=\"_blank\" href=\"https://www.kaggle.com/c/forest-cover-type-prediction/overview\">Forest Cover Type Prediction\n</a> competition.</p>\n<p>Good luck and have fun!</p>\n<p>For ideas on how to improve your score, check out the <a aria-label=\"Intro to Machine Learning (opens in a new tab)\" target=\"_blank\" href=\"https://www.kaggle.com/learn/intro-to-machine-learning\">Intro to Machine Learning</a> and <a aria-label=\"Intermediate Machine Learning (opens in a new tab)\" target=\"_blank\" href=\"https://www.kaggle.com/learn/intermediate-machine-learning\">Intermediate Machine Learning</a> courses on Kaggle Learn.</p>", "Evaluation": "<p>Submissions are evaluated on multi-class classification accuracy.</p>\n<h2>Submission File</h2>\n<p>For each <code>Id</code> in the test set, you must predict the <code>Cover_Type</code> class. The file should contain a header and have the following format:</p>\n<pre class=\"uc-code-block\"><code><span class=\"hljs-attribute\">Id</span>,Cover_Type\n<span class=\"hljs-attribute\">4000000</span>,<span class=\"hljs-number\">2</span>\n<span class=\"hljs-attribute\">4000001</span>,<span class=\"hljs-number\">1</span>\n<span class=\"hljs-attribute\">4000001</span>,<span class=\"hljs-number\">3</span>\n<span class=\"hljs-attribute\">etc</span>.\n</code><div class=\"uc-code-block-copy-button-wrapper\"><button class=\"uc-code-block-copy-button google-symbols\" aria-label=\"Copy code\">content_copy</button></div></pre>", "Timeline": "<ul>\n<li><strong>Start Date</strong> - December 1, 2021</li>\n<li><strong>Entry deadline</strong> - Same as the Final Submission Deadline</li>\n<li><strong>Team Merger deadline</strong> - Same as the Final Submission Deadline</li>\n<li><strong>Final submission deadline</strong> -  December 31, 2021</li>\n</ul>\n<p>All deadlines are at 11:59 PM UTC on the corresponding day unless otherwise noted. The competition organizers reserve the right to update the contest timeline if they deem it necessary.</p>", "Prizes": "<ul>\n<li>1st Place - Choice of Kaggle merchandise</li>\n<li>2nd Place - Choice of Kaggle merchandise</li>\n<li>3rd Place - Choice of Kaggle merchandise</li>\n</ul>\n<p><strong>Please note:</strong> In order to encourage more participation from beginners, Kaggle merchandise will only be awarded once per person in this series. If a person has previously won, we'll skip to the next team. </p>", "Citation": "<PERSON>. Tabular Playground Series - Dec 2021. https://kaggle.com/competitions/tabular-playground-series-dec-2021, 2021. Kaggle.", "Data Description": "<p>For this competition, you will be predicting a categorical target based on a number of feature columns given in the data. <br>\nThe data is synthetically generated by a GAN that was trained on a the data from the <a aria-label=\"Forest Cover Type Prediction (opens in a new tab)\" target=\"_blank\" href=\"https://www.kaggle.com/c/forest-cover-type-prediction/overview\">Forest Cover Type Prediction</a>. This dataset is (a) much larger, and (b) may or may not have the same relationship to the target as the original data.</p>\n<p>Please refer to this <a aria-label=\"data page (opens in a new tab)\" target=\"_blank\" href=\"https://www.kaggle.com/c/forest-cover-type-prediction/data\">data page</a> for a detailed explanation of the features.</p>\n<h2>Files</h2>\n<ul>\n<li><strong>train.csv</strong> - the training data with the target <code>Cover_Type</code> column</li>\n<li><strong>test.csv</strong> - the test set; you will be predicting the <code>Cover_Type</code> for each row in this file (the target integer class)</li>\n<li><strong>sample_submission.csv</strong> - a sample submission file in the correct format</li>\n</ul>"}