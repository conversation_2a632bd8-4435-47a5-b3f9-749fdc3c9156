updates:
  - commit-message:
      prefix: build(actions)
    directory: /
    package-ecosystem: github-actions
    schedule:
      interval: weekly
  - commit-message:
      prefix: build(requirements)
    directory: /
    groups:
      dev:
        dependency-type: development
      prod:
        dependency-type: production
    package-ecosystem: pip
    schedule:
      interval: weekly
version: 2
