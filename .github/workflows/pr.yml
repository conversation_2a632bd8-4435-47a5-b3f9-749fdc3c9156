name: Lint pull request title

on:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
      - edited

concurrency:
  cancel-in-progress: true
  group: ${{ github.workflow }}-${{ github.ref }}

jobs:
  lint-title:
    runs-on: ubuntu-latest
    steps:
      # This step is necessary because the lint title uses the .commitlintrc.js file in the project root directory.
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '16'

      - name: Install commitlint
        run: npm install --save-dev @commitlint/{config-conventional,cli}

      - name: Validate PR Title with commitlint
        env:
          BODY: ${{ github.event.pull_request.title }}
        run: |
          echo "$BODY" | npx commitlint --config .commitlintrc.js
