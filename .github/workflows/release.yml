name: Release
on:
  push:
    branches:
      - main
permissions:
  contents: read
jobs:
  release_and_publish:
    permissions:
      contents: write
      pull-requests: read
    runs-on: ubuntu-latest
    steps:
      - name: Release please
        id: release_please
        uses: googleapis/release-please-action@v4
        with:
          # The current PAT (personal access token) was created on 2024-08-05,
          # since the maximum validity of PAT is 1 year, you need to change the PAT before 2025-08-05.
          token: ${{ secrets.PAT }}
          release-type: simple
      - uses: actions/checkout@v4
        if: ${{ steps.release_please.outputs.release_created }}
        with:
          fetch-depth: 0
      - name: Set up Python
        if: ${{ steps.release_please.outputs.release_created }}
        uses: actions/setup-python@v5
        with:
          cache: pip
          python-version: '3.10'
      - name: Install dependencies
        if: ${{ steps.release_please.outputs.release_created }}
        run: |
          python -m pip install --upgrade pip
          pip install setuptools wheel twine  #  better-exceptions(optional for debug)
      - run: env | sort
        if: ${{ steps.release_please.outputs.release_created }}
      - run: make dev
        if: ${{ steps.release_please.outputs.release_created }}
      - run: make build
        if: ${{ steps.release_please.outputs.release_created }}
      - name: upload
        if: ${{ steps.release_please.outputs.release_created }}
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN }}
        run: |
          make upload
