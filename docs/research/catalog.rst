===========
Research
===========

To achieve the good effects and improve R&D capabilities, we face multiple challenges, the most important of which is the continuous evolution capability. Existing large language models (LLMs) find it difficult to continue growing their capabilities after training is completed. Moreover, the training process of LLMs focuses more on general knowledge, and the lack of depth in more specialized knowledge becomes an obstacle to solving professional R&D problems within the industry. This specialized knowledge needs to be learned and acquired from in-depth industry practice.


Our RD-Agent, on the other hand, can continuously acquire in-depth domain knowledge through deep exploration during the R&D phase, allowing its R&D capabilities to keep growing.

To address these key challenges and achieve industrial value, a series of research work needs to be completed.


.. list-table:: Research Areas and Descriptions
   :header-rows: 1

   * - Research Area
     - Description
   * - :doc:`Benchmark <benchmark>`
     - Benchmark the R&D abilities
   * - Research
     - Idea proposal: Explore new ideas or refine existing ones
   * - :doc:`Development <dev>`
     - Ability to realize ideas: Implement and execute ideas




.. toctree::
   :maxdepth: 1
   :caption: Doctree:
   :hidden:

   benchmark
   dev
