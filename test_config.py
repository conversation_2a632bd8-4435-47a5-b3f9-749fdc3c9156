from dotenv import load_dotenv
load_dotenv()  # 加载 .env 文件中的环境变量

from openai import OpenAI
import os

# 打印环境变量
print(os.getenv("OPENAI_API_BASE"))
print(os.getenv("OPENAI_API_KEY"))
print(os.getenv("CHAT_MODEL"))

# 创建OpenAI客户端
client = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_API_BASE")
)

# 测试聊天模型
def test_openai_config():
    try:
        # 使用聊天模型发送一个简单的 API 请求
        response = client.chat.completions.create(
            model=os.getenv("CHAT_MODEL"),
            messages=[
                {"role": "user", "content": "Hello, how are you?"}
            ],
            # max_tokens=5
        )
        print("Chat API Test Successful!")
        print(f"Response: {response.choices[0].message.content.strip()}")
    except Exception as e:
        print("Chat API Test Failed")
        print(f"Error: {str(e)}")

# 测试嵌入模型
def test_embedding_model():
    try:
        # 使用指定的嵌入模型对一个简单的文本进行嵌入
        embedding_response = client.embeddings.create(
            model=os.getenv("EMBEDDING_MODEL"),
            input="This is a test sentence for embedding."
        )
        print("Embedding Model Test Successful!")
        print(f"Embedding: {embedding_response.data[0].embedding[:10]}...")  # 打印嵌入的前10个数值
    except Exception as e:
        print("Embedding Model Test Failed")
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_openai_config()
    test_embedding_model()
